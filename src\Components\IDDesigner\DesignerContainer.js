import { useEffect, useCallback, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Box, Paper, Toolbar, IconButton, Tooltip, Divider, TextField } from '@mui/material';
import {
  Undo as UndoIcon,
  Redo as RedoIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Visibility as PreviewIcon,
  Save as SaveIcon,
  FolderOpen as OpenIcon,
  Settings as SettingsIcon,
  AspectRatio as CanvasIcon,
  FormatSize as FontSizeIcon,
} from '@mui/icons-material';

import DesignCanvas from './DesignCanvas';
import ToolPalette from './ToolPalette';
import PropertyPanel from './PropertyPanel';
import PreviewPane from './PreviewPane';
import TemplateManager from './TemplateManager';

import {
  undo,
  redo,
  setCanvasScale,
  togglePreviewMode,
  togglePropertyPanel,
  toggleCanvasSettings,
  togglePreviewPane,
  toggleTemplateManager,
  updateCanvasConfig,
  clearSelection,
} from '../../redux/idDesignerSlice';

const DesignerContainer = () => {
  const dispatch = useDispatch();
  const [zoomInput, setZoomInput] = useState('');
  const [fontSize, setFontSize] = useState(12);

  const {
    canvasConfig,
    elements,
    history,
    previewMode,
    showPropertyPanel,
    showCanvasSettings,
    showPreviewPane,
    showTemplateManager,
    currentTemplate,
    selectedElementIds,
  } = useSelector((state) => state.idDesigner);

  // Keyboard shortcuts
  const handleKeyDown = useCallback((event) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'z':
          event.preventDefault();
          if (event.shiftKey) {
            dispatch(redo());
          } else {
            dispatch(undo());
          }
          break;
        case 'y':
          event.preventDefault();
          dispatch(redo());
          break;
        case 's':
          event.preventDefault();
          // TODO: Implement save functionality
          break;
        case 'o':
          event.preventDefault();
          dispatch(toggleTemplateManager());
          break;
        default:
          break;
      }
    }
  }, [dispatch]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Sync zoom input with canvas scale
  useEffect(() => {
    setZoomInput(Math.round(canvasConfig.scale * 100).toString());
  }, [canvasConfig.scale]);

  const handleZoomIn = () => {
    const newScale = Math.min(canvasConfig.scale * 1.2, 3);
    dispatch(setCanvasScale(newScale));
  };

  const handleZoomOut = () => {
    const newScale = Math.max(canvasConfig.scale / 1.2, 0.1);
    dispatch(setCanvasScale(newScale));
  };

  const handleZoomReset = () => {
    dispatch(setCanvasScale(1));
    setZoomInput('100');
  };

  const handleZoomInputChange = (e) => {
    const value = e.target.value;
    setZoomInput(value);
  };

  const handleZoomInputSubmit = (e) => {
    if (e.key === 'Enter' || e.type === 'blur') {
      const numValue = parseFloat(zoomInput);
      if (!isNaN(numValue) && numValue > 0) {
        const scale = Math.max(0.1, Math.min(3, numValue / 100));
        dispatch(setCanvasScale(scale));
      }
      setZoomInput(Math.round(canvasConfig.scale * 100).toString());
    }
  };

  const handleFontSizeChange = (delta) => {
    setFontSize(prev => Math.max(8, Math.min(72, prev + delta)));
  };

  const handleCanvasSettingsClick = () => {
    dispatch(clearSelection());
    dispatch(toggleCanvasSettings());
    // Ensure property panel is open when canvas settings are toggled
    if (!showPropertyPanel) {
      dispatch(togglePropertyPanel());
    }
  };

  const handlePropertyPanelToggle = () => {
    // If property panel is being closed, also close canvas settings
    if (showPropertyPanel && showCanvasSettings) {
      dispatch(toggleCanvasSettings());
    }
    dispatch(togglePropertyPanel());
  };

  return (
    <Box sx={{
      height: 'calc(100%)',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: '#f5f5f5',
      borderRadius: 2,
      overflow: 'hidden',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      border: '1px solid #e5e7eb'
    }}>
      {/* Top Toolbar */}
      <Paper elevation={1} sx={{ zIndex: 1000 }}>
        <Toolbar variant="dense" sx={{ minHeight: 48, gap: 1 }}>
          {/* File Operations */}
          <Tooltip title="Open Template (Ctrl+O)">
            <IconButton
              size="small"
              onClick={() => dispatch(toggleTemplateManager())}
              color={showTemplateManager ? 'primary' : 'default'}
            >
              <OpenIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Save Template (Ctrl+S)">
            <IconButton
              size="small"
              onClick={() => {
                const templateData = {
                  id: currentTemplate.id || Date.now(),
                  name: currentTemplate.name || 'Untitled Template',
                  version: currentTemplate.version || '1.0.0',
                  lastModified: new Date().toISOString(),
                  canvasConfig: canvasConfig,
                  elements: elements,
                  createdAt: new Date().toISOString(),
                  description: 'ID Card Template'
                };
                console.log('Template Data (JSON):', JSON.stringify(templateData, null, 2));
              }}
            >
              <SaveIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* History Operations */}
          <Tooltip title="Undo (Ctrl+Z)">
            <IconButton
              size="small"
              onClick={() => dispatch(undo())}
              disabled={history.past.length === 0}
            >
              <UndoIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Redo (Ctrl+Y)">
            <IconButton
              size="small"
              onClick={() => dispatch(redo())}
              disabled={history.future.length === 0}
            >
              <RedoIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* Zoom Controls */}
          <Tooltip title="Zoom Out">
            <IconButton size="small" onClick={handleZoomOut}>
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>

          <TextField
            value={zoomInput}
            onChange={handleZoomInputChange}
            onKeyDown={handleZoomInputSubmit}
            onBlur={handleZoomInputSubmit}
            size="small"
            variant="outlined"
            sx={{
              width: 70,
              '& .MuiOutlinedInput-root': {
                height: 32,
                fontSize: '0.875rem',
                '& input': {
                  textAlign: 'center',
                  padding: '4px 8px',
                }
              }
            }}
            slotProps={{
              input: {
                endAdornment: '%'
              }
            }}
          />

          <Tooltip title="Zoom In">
            <IconButton size="small" onClick={handleZoomIn}>
              <ZoomInIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* Font Size Controls */}
          <Tooltip title="Decrease Font Size">
            <IconButton size="small" onClick={() => handleFontSizeChange(-1)}>
              <FontSizeIcon sx={{ fontSize: 16 }} />
            </IconButton>
          </Tooltip>

          <Box
            sx={{
              minWidth: 40,
              textAlign: 'center',
              fontSize: '0.75rem',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              bgcolor: 'action.hover',
            }}
          >
            {fontSize}px
          </Box>

          <Tooltip title="Increase Font Size">
            <IconButton size="small" onClick={() => handleFontSizeChange(1)}>
              <FontSizeIcon sx={{ fontSize: 20 }} />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* View Controls */}
          <Tooltip title="Toggle Preview Mode">
            <IconButton
              size="small"
              onClick={() => dispatch(togglePreviewMode())}
              color={previewMode ? 'primary' : 'default'}
            >
              <PreviewIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Toggle Property Panel">
            <IconButton
              size="small"
              onClick={handlePropertyPanelToggle}
              color={showPropertyPanel ? 'primary' : 'default'}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>

          {/* Canvas Settings */}
          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Canvas Settings">
              <IconButton
                size="small"
                color={showCanvasSettings ? 'primary' : 'default'}
                onClick={handleCanvasSettingsClick}
              >
                <CanvasIcon />
              </IconButton>
            </Tooltip>
            <Box sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
              {canvasConfig.width} × {canvasConfig.height}
            </Box>
          </Box>
        </Toolbar>
      </Paper>

      {/* Main Content Area */}
      <Box sx={{
        width: '100%',
        height: 'calc(100vh - 48px)', // Subtract toolbar height
        display: 'flex',
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* Left Sidebar - Tool Palette */}
        <Paper
          elevation={2}
          sx={{
            width: '20%',
            display: 'flex',
            flexDirection: 'column',
            borderRadius: 0,
            borderRight: 1,
            borderColor: 'divider',
            bgcolor: 'white',
            boxShadow: '2px 0 4px rgba(0, 0, 0, 0.1)',
          }}
        >
          <ToolPalette />
        </Paper>

        {/* Center - Canvas Area */}
        <Box
          sx={{
            width: (showPropertyPanel && (selectedElementIds.length > 0 || (showCanvasSettings && selectedElementIds.length === 0))) ? '60%' : '80%',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            overflow: 'hidden',
            transition: 'width 0.3s ease',
          }}
        >
          <DesignCanvas />
        </Box>

        {/* Right Sidebar - Property Panel (Conditional visibility) */}
        {(showPropertyPanel && (selectedElementIds.length > 0 || (showCanvasSettings && selectedElementIds.length === 0))) && (
          <Paper
            elevation={2}
            sx={{
              width: '20%',
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 0,
              borderLeft: 1,
              borderColor: 'divider',
              bgcolor: 'white',
              boxShadow: '-2px 0 4px rgba(0, 0, 0, 0.1)',
            }}
          >
            <PropertyPanel />
          </Paper>
        )}

        {/* Preview Pane */}
        {showPreviewPane && (
          <Paper
            elevation={1}
            sx={{
              position: 'absolute',
              right: '20%',
              top: 0,
              width: 400,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 0,
              borderLeft: 1,
              borderColor: 'divider',
              zIndex: 1000,
            }}
          >
            <PreviewPane />
          </Paper>
        )}
      </Box>

      {/* Template Manager Modal */}
      {showTemplateManager && <TemplateManager />}
    </Box>
  );
};

export default DesignerContainer;
