import { createSlice } from '@reduxjs/toolkit';
import { v4 as uuidv4 } from 'uuid';
import { UNITS, DEFAULT_DPI, inchesToPixels, getCardSizeInPixels } from '../utils/unitConversion';

const initialState = {
  // Canvas configuration
  canvasConfig: {
    width: inchesToPixels(3.375, DEFAULT_DPI), // Standard CR80 card width
    height: inchesToPixels(2.125, DEFAULT_DPI), // Standard CR80 card height
    background: '#ffffff',
    scale: 1,
    offsetX: 0,
    offsetY: 0,
    snapToGrid: true,
    gridSize: inchesToPixels(0.1, DEFAULT_DPI), // 0.1 inch grid
    showGrid: true,
    showRulers: true,
    unit: UNITS.INCHES,
    dpi: DEFAULT_DPI,
    cardType: 'CR80 (Standard)',
  },
  
  // Design elements
  elements: [],
  
  // Selection and interaction
  selectedElementIds: [],
  
  // History for undo/redo
  history: {
    past: [],
    future: [],
  },
  
  // Template management
  currentTemplate: {
    id: null,
    name: 'Untitled Template',
    version: '1.0.0',
    lastModified: null,
  },
  
  // Preview mode
  previewMode: false,
  mockData: {
    name: '<PERSON>e',
    id: 'EMP001',
    department: 'Engineering',
    photo: null,
    qrData: 'EMP001-JOHN-DOE',
  },
  
  // UI state
  showPropertyPanel: true,
  showCanvasSettings: false, // New state for canvas settings in property panel
  showPreviewPane: false,
  showTemplateManager: false,
  
  // Loading states
  loading: false,
  saving: false,
  error: null,
};

const idDesignerSlice = createSlice({
  name: 'idDesigner',
  initialState,
  reducers: {
    // Canvas actions
    updateCanvasConfig: (state, action) => {
      state.canvasConfig = { ...state.canvasConfig, ...action.payload };
    },
    
    setCanvasScale: (state, action) => {
      state.canvasConfig.scale = action.payload;
    },
    
    setCanvasOffset: (state, action) => {
      state.canvasConfig.offsetX = action.payload.x;
      state.canvasConfig.offsetY = action.payload.y;
    },

    setCanvasUnit: (state, action) => {
      state.canvasConfig.unit = action.payload;
    },

    setCanvasDPI: (state, action) => {
      state.canvasConfig.dpi = action.payload;
    },

    toggleRulers: (state) => {
      state.canvasConfig.showRulers = !state.canvasConfig.showRulers;
    },

    setCardType: (state, action) => {
      const { cardType, unit, dpi } = action.payload;
      state.canvasConfig.cardType = cardType;

      // Update canvas dimensions based on card type
      const cardSize = getCardSizeInPixels(cardType, unit, dpi);
      if (cardSize) {
        state.canvasConfig.width = cardSize.width;
        state.canvasConfig.height = cardSize.height;
      }
    },
    
    // Element management
    addElement: (state, action) => {
      const newElement = {
        id: uuidv4(),
        type: action.payload.type,
        x: action.payload.x || 100,
        y: action.payload.y || 100,
        width: action.payload.width || 100,
        height: action.payload.height || 50,
        rotation: 0,
        zIndex: state.elements.length,
        locked: false,
        visible: true,
        ...action.payload,
      };

      // Save current state to history
      state.history.past.push({
        elements: JSON.parse(JSON.stringify(state.elements)),
        selectedElementIds: [...state.selectedElementIds],
      });
      state.history.future = [];

      state.elements.push(newElement);
      state.selectedElementIds = [newElement.id];

      // Close canvas settings when adding an element and ensure property panel is open
      state.showCanvasSettings = false;
      state.showPropertyPanel = true;
    },
    
    updateElement: (state, action) => {
      const { id, properties } = action.payload;
      const elementIndex = state.elements.findIndex(el => el.id === id);

      if (elementIndex !== -1) {
        // Save current state to history only for significant changes
        const significantProps = ['x', 'y', 'width', 'height', 'rotation', 'text', 'src'];
        const hasSignificantChange = Object.keys(properties).some(key =>
          significantProps.includes(key)
        );

        if (hasSignificantChange) {
          state.history.past.push({
            elements: JSON.parse(JSON.stringify(state.elements)),
            selectedElementIds: [...state.selectedElementIds],
          });
          state.history.future = [];

          // Limit history size
          if (state.history.past.length > 50) {
            state.history.past = state.history.past.slice(-50);
          }
        }

        state.elements[elementIndex] = {
          ...state.elements[elementIndex],
          ...properties,
        };
      }
    },
    
    deleteElement: (state, action) => {
      const elementId = action.payload;
      
      // Save current state to history
      state.history.past.push({
        elements: [...state.elements],
        selectedElementIds: [...state.selectedElementIds],
      });
      state.history.future = [];
      
      state.elements = state.elements.filter(el => el.id !== elementId);
      state.selectedElementIds = state.selectedElementIds.filter(id => id !== elementId);

      // If no elements are selected after deletion and canvas settings are not open,
      // the property panel will be hidden by the UI logic
    },
    
    duplicateElement: (state, action) => {
      const elementId = action.payload;
      const element = state.elements.find(el => el.id === elementId);
      
      if (element) {
        const duplicatedElement = {
          ...element,
          id: uuidv4(),
          x: element.x + 20,
          y: element.y + 20,
          zIndex: state.elements.length,
        };
        
        // Save current state to history
        state.history.past.push({
          elements: [...state.elements],
          selectedElementIds: [...state.selectedElementIds],
        });
        state.history.future = [];
        
        state.elements.push(duplicatedElement);
        state.selectedElementIds = [duplicatedElement.id];

        // Close canvas settings when duplicating and ensure property panel is open
        state.showCanvasSettings = false;
        state.showPropertyPanel = true;
      }
    },
    
    // Selection management
    selectElement: (state, action) => {
      const { id, multiSelect = false } = action.payload;

      if (multiSelect) {
        if (state.selectedElementIds.includes(id)) {
          state.selectedElementIds = state.selectedElementIds.filter(selectedId => selectedId !== id);
        } else {
          state.selectedElementIds.push(id);
        }
      } else {
        state.selectedElementIds = [id];
      }

      // Close canvas settings when selecting elements and ensure property panel is open
      state.showCanvasSettings = false;
      if (state.selectedElementIds.length > 0) {
        state.showPropertyPanel = true;
      }
    },
    
    selectMultipleElements: (state, action) => {
      state.selectedElementIds = action.payload;

      // Close canvas settings when selecting multiple elements and ensure property panel is open
      if (action.payload.length > 0) {
        state.showCanvasSettings = false;
        state.showPropertyPanel = true;
      }
    },
    
    clearSelection: (state) => {
      state.selectedElementIds = [];
      // If canvas settings are not open and no elements are selected,
      // the property panel can be closed (but don't force it)
    },
    
    // Layer management
    moveElementToFront: (state, action) => {
      const elementId = action.payload;
      const element = state.elements.find(el => el.id === elementId);
      
      if (element) {
        const maxZIndex = Math.max(...state.elements.map(el => el.zIndex));
        element.zIndex = maxZIndex + 1;
      }
    },
    
    moveElementToBack: (state, action) => {
      const elementId = action.payload;
      const element = state.elements.find(el => el.id === elementId);
      
      if (element) {
        const minZIndex = Math.min(...state.elements.map(el => el.zIndex));
        element.zIndex = minZIndex - 1;
      }
    },
    
    // History management
    undo: (state) => {
      if (state.history.past.length > 0) {
        const previous = state.history.past.pop();
        state.history.future.push({
          elements: [...state.elements],
          selectedElementIds: [...state.selectedElementIds],
        });
        
        state.elements = previous.elements;
        state.selectedElementIds = previous.selectedElementIds;
      }
    },
    
    redo: (state) => {
      if (state.history.future.length > 0) {
        const next = state.history.future.pop();
        state.history.past.push({
          elements: [...state.elements],
          selectedElementIds: [...state.selectedElementIds],
        });
        
        state.elements = next.elements;
        state.selectedElementIds = next.selectedElementIds;
      }
    },
    
    clearHistory: (state) => {
      state.history.past = [];
      state.history.future = [];
    },
    
    // Template management
    setCurrentTemplate: (state, action) => {
      state.currentTemplate = { ...state.currentTemplate, ...action.payload };
    },
    
    loadTemplate: (state, action) => {
      const template = action.payload;
      state.elements = template.elements || [];
      state.canvasConfig = { ...state.canvasConfig, ...template.canvasConfig };
      state.currentTemplate = {
        id: template.id,
        name: template.name,
        version: template.version,
        lastModified: template.lastModified,
      };
      state.selectedElementIds = [];
      state.history.past = [];
      state.history.future = [];
    },
    
    // Preview mode
    togglePreviewMode: (state) => {
      state.previewMode = !state.previewMode;
    },
    
    updateMockData: (state, action) => {
      state.mockData = { ...state.mockData, ...action.payload };
    },
    
    // UI state
    togglePropertyPanel: (state) => {
      state.showPropertyPanel = !state.showPropertyPanel;
    },

    toggleCanvasSettings: (state) => {
      state.showCanvasSettings = !state.showCanvasSettings;
    },

    togglePreviewPane: (state) => {
      state.showPreviewPane = !state.showPreviewPane;
    },
    
    toggleTemplateManager: (state) => {
      state.showTemplateManager = !state.showTemplateManager;
    },
    
    // Loading states
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    
    setSaving: (state, action) => {
      state.saving = action.payload;
    },
    
    setError: (state, action) => {
      state.error = action.payload;
    },
    
    // Reset state
    resetDesigner: (state) => {
      return { ...initialState };
    },
  },
});

export const {
  updateCanvasConfig,
  setCanvasScale,
  setCanvasOffset,
  setCanvasUnit,
  setCanvasDPI,
  toggleRulers,
  setCardType,
  addElement,
  updateElement,
  deleteElement,
  duplicateElement,
  selectElement,
  selectMultipleElements,
  clearSelection,
  moveElementToFront,
  moveElementToBack,
  undo,
  redo,
  clearHistory,
  setCurrentTemplate,
  loadTemplate,
  togglePreviewMode,
  updateMockData,
  togglePropertyPanel,
  toggleCanvasSettings,
  togglePreviewPane,
  toggleTemplateManager,
  setLoading,
  setSaving,
  setError,
  resetDesigner,
} = idDesignerSlice.actions;

export default idDesignerSlice.reducer;
